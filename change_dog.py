# 导入必要的库
from google import genai  # Google Generative AI 库
from google.genai import types  # Google Generative AI 类型定义
from PIL import Image  # Python 图像处理库
from io import BytesIO  # 字节流处理
import os  # 操作系统接口
import base64  # Base64 编码解码

import PIL.Image  # PIL 图像处理模块

# Google API 密钥
API_KEY = "AIzaSyCeJFDSzuefKRA2tb3l1b7vUipBQc9CxGI"

# 创建输出目录
output_dir = "generated_images"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 加载要处理的图片（需要替换为实际的图片路径）
image = PIL.Image.open('dog.jpg')

# 创建 Google Generative AI 客户端，使用 API 密钥进行身份验证
client = genai.Client(api_key=API_KEY)

# 定义文本输入，描述要对图片进行的修改
text_input = ('这张图片里是一只狗。请你将它更换服装、帽子、手上的物品等。'
            '请保持狗的品种不变，并生成一张高质量的艺术图片。',)

# 设置生成次数
generation_count = 5  # 可以修改这个数字来改变生成次数

# 循环生成图片
total_image_count = 0  # 总图片计数器
for i in range(generation_count):
    print(f"正在进行第 {i+1}/{generation_count} 次生成...")

    # 调用 Gemini 模型生成内容
    # 使用支持图像生成的 Gemini 2.0 Flash 预览版模型
    response = client.models.generate_content(
        model="gemini-2.0-flash-preview-image-generation",  # 指定模型
        contents=[text_input, image],  # 输入内容：文本描述和原始图片
        config=types.GenerateContentConfig(
          response_modalities=['TEXT', 'IMAGE']  # 指定响应类型：文本和图像
        )
    )

    # 处理模型返回的结果
    for part in response.candidates[0].content.parts:
      if part.text is not None:
        # 如果返回的是文本内容，打印到控制台
        print("AI 回复:", part.text)
      elif part.inline_data is not None:
        try:
            # 打印调试信息
            print(f"接收到图像数据，MIME类型: {part.inline_data.mime_type}")

            # 获取图像数据
            image_data = part.inline_data.data

            # 检查数据类型并处理
            print(f"原始数据类型: {type(image_data)}")
            print(f"原始数据长度: {len(image_data) if hasattr(image_data, '__len__') else 'Unknown'}")

            if isinstance(image_data, str):
                # 如果是字符串，解码base64
                print("数据是字符串格式，进行base64解码...")
                image_data = base64.b64decode(image_data)
            elif hasattr(image_data, 'decode'):
                # 如果是bytes类型但需要解码
                print("数据是bytes格式，尝试直接使用...")
            else:
                print(f"未知数据格式: {type(image_data)}")

            # 检查数据的前几个字节（文件头）
            if len(image_data) > 10:
                header_bytes = image_data[:10]
                print(f"文件头字节: {header_bytes}")
                print(f"文件头十六进制: {header_bytes.hex()}")

            # 先保存原始数据到文件，然后尝试用PIL打开文件
            temp_filename = f"temp_image_{total_image_count:03d}.bin"
            temp_filepath = os.path.join(output_dir, temp_filename)

            with open(temp_filepath, 'wb') as f:
                f.write(image_data)
            print(f"临时文件已保存: {temp_filepath}")

            # 尝试用PIL打开保存的文件
            generated_image = Image.open(temp_filepath)
            print(f"成功打开图像！格式: {generated_image.format}, 尺寸: {generated_image.size}")

            # 根据检测到的格式确定扩展名
            if generated_image.format:
                ext = generated_image.format.lower()
                if ext == 'jpeg':
                    ext = 'jpg'
            else:
                ext = 'png'  # 默认使用png

            # 生成简化的文件名
            filename = f"image_{total_image_count:03d}.{ext}"
            filepath = os.path.join(output_dir, filename)

            # 保存图片到本地
            generated_image.save(filepath)
            print(f"图片已保存到: {filepath}")

            # 显示生成的图片
            generated_image.show()

            # 删除临时文件
            os.remove(temp_filepath)
            print(f"临时文件已删除: {temp_filepath}")

            total_image_count += 1

        except Exception as e:
            print(f"处理图像时出错: {e}")
            print("尝试其他方法保存图像数据...")

            # 尝试根据MIME类型确定文件扩展名
            mime_type = part.inline_data.mime_type if hasattr(part.inline_data, 'mime_type') else 'unknown'
            if 'jpeg' in mime_type or 'jpg' in mime_type:
                ext = 'jpg'
            elif 'png' in mime_type:
                ext = 'png'
            elif 'webp' in mime_type:
                ext = 'webp'
            else:
                ext = 'bin'

            # 尝试直接保存原始数据
            try:
                filename = f"fallback_image_{total_image_count:03d}.{ext}"
                filepath = os.path.join(output_dir, filename)

                image_data = part.inline_data.data
                if isinstance(image_data, str):
                    image_data = base64.b64decode(image_data)

                with open(filepath, 'wb') as f:
                    f.write(image_data)
                print(f"备用方案：图像数据已保存到: {filepath}")

                # 尝试用PIL重新打开保存的文件
                try:
                    test_image = Image.open(filepath)
                    print(f"备用方案成功！图像格式: {test_image.format}, 尺寸: {test_image.size}")
                    test_image.show()
                    total_image_count += 1
                except Exception as open_error:
                    print(f"备用方案也无法打开文件: {open_error}")
                    print("文件已保存，但可能不是有效的图像格式")

            except Exception as save_error:
                print(f"备用保存方案也失败: {save_error}")

print(f"处理完成！共生成 {total_image_count} 张图片")