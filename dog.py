import google.generativeai as genai
import random
import time
import os
from PIL import Image

# 设置API密钥
API_KEY = "AIzaSyCeJFDSzuefKRA2tb3l1b7vUipBQc9CxGI"

# 配置API密钥
genai.configure(api_key=API_KEY)

def generate_dog_image_prompt():
    """生成随机的狗狗图片提示词"""

    # 服装选项
    clothing_options = [
        "穿着优雅的燕尾服", "穿着可爱的连衣裙", "穿着酷炫的皮夹克",
        "穿着温暖的毛衣", "穿着运动服", "穿着超级英雄服装",
        "穿着古典的维多利亚时代服装", "穿着现代的休闲装", "穿着传统的和服",
        "穿着牛仔服装", "穿着医生白大褂", "穿着厨师服装"
    ]

    # 帽子选项
    hat_options = [
        "戴着时尚的贝雷帽", "戴着可爱的棒球帽", "戴着优雅的礼帽",
        "戴着温暖的毛线帽", "戴着酷炫的墨镜", "戴着花环",
        "戴着牛仔帽", "戴着厨师帽", "戴着圣诞帽", "戴着皇冠",
        "戴着头巾", "戴着船长帽"
    ]

    # 手持物品选项
    items_options = [
        "手里拿着一束鲜花", "手里拿着一本书", "手里拿着咖啡杯",
        "手里拿着画笔", "手里拿着魔法棒", "手里拿着小提琴",
        "手里拿着相机", "手里拿着气球", "手里拿着冰淇淋",
        "手里拿着雨伞", "手里拿着望远镜", "手里拿着小铃铛"
    ]

    # 艺术风格选项
    style_options = [
        "卡通风格", "写实风格", "水彩画风格", "油画风格",
        "动漫风格", "像素艺术风格", "印象派风格", "抽象艺术风格",
        "复古风格", "现代艺术风格", "梦幻风格", "蒸汽朋克风格"
    ]

    # 随机选择元素
    clothing = random.choice(clothing_options)
    hat = random.choice(hat_options)
    item = random.choice(items_options)
    style = random.choice(style_options)

    # 构建提示词
    prompt = f"""这张图片里是一只狗。请你将它更换服装、帽子、手上的物品等。请保持狗的品种不变，并生成一张高质量的艺术图片。

具体要求：
- {clothing}
- {hat}
- {item}
- 采用{style}
- 图片风格要有趣且富有创意
- 保持高质量的艺术效果
- 确保狗狗的品种特征不变"""

    return prompt, {"服装": clothing, "帽子": hat, "物品": item, "风格": style}

def generate_dog_images(image_path, num_images=5, delay_seconds=2):
    """生成多张不同风格的狗狗图片"""

    # 检查图片文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：找不到图片文件 {image_path}")
        print("请确保图片文件路径正确")
        return

    # 加载原始图片
    try:
        image = Image.open(image_path)
        print(f"成功加载图片：{image_path}")
        print(f"图片尺寸：{image.size}")
    except Exception as e:
        print(f"加载图片时出错：{e}")
        return

    # 初始化模型
    model = genai.GenerativeModel('gemini-1.5-flash')

    print(f"\n开始生成 {num_images} 张不同风格的狗狗图片...")
    print("=" * 50)

    for i in range(num_images):
        try:
            # 生成随机提示词
            prompt, elements = generate_dog_image_prompt()

            print(f"\n第 {i+1} 张图片生成中...")
            print(f"服装：{elements['服装']}")
            print(f"帽子：{elements['帽子']}")
            print(f"物品：{elements['物品']}")
            print(f"风格：{elements['风格']}")

            # 调用API生成内容（注意：标准的gemini模型不支持图片生成，这里先生成文本描述）
            response = model.generate_content([prompt, image])

            print(f"AI回复：{response.text}")

            # 保存描述到文件
            description_filename = f"dog_style_{i+1}_description.txt"
            with open(description_filename, 'w', encoding='utf-8') as f:
                f.write(f"=== 第 {i+1} 张狗狗艺术图片描述 ===\n\n")
                f.write(f"服装：{elements['服装']}\n")
                f.write(f"帽子：{elements['帽子']}\n")
                f.write(f"物品：{elements['物品']}\n")
                f.write(f"风格：{elements['风格']}\n\n")
                f.write("=== 原始提示词 ===\n")
                f.write(f"{prompt}\n\n")
                f.write("=== AI生成的详细描述 ===\n")
                f.write(f"{response.text}\n")

            print(f"描述已保存为：{description_filename}")
            print(f"第 {i+1} 张图片描述生成完成！")

            # 如果不是最后一张图片，等待一段时间
            if i < num_images - 1:
                print(f"等待 {delay_seconds} 秒后生成下一张...")
                time.sleep(delay_seconds)

        except Exception as e:
            print(f"生成第 {i+1} 张图片时出错：{e}")
            continue

    print("\n" + "=" * 50)
    print("所有图片生成完成！")

if __name__ == "__main__":
    # 设置图片路径
    image_path = "dog.jpg"  # 使用当前目录中的狗狗图片

    # 生成5张不同风格的图片，每张间隔2秒
    generate_dog_images(image_path, num_images=5, delay_seconds=2)