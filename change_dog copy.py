# 导入必要的库
from google import genai  # Google Generative AI 库
from google.genai import types  # Google Generative AI 类型定义
from PIL import Image  # Python 图像处理库
from io import BytesIO  # 字节流处理
import os  # 操作系统接口
import base64  # Base64 编码解码

import PIL.Image  # PIL 图像处理模块

# Google API 密钥 (使用test.py中验证过的密钥)
API_KEY = "AIzaSyD4sbwn16i4TNjk8JonQ8n-5liutAMZuTs"

# 创建输出目录
output_dir = "generated_images"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 加载要处理的图片（需要替换为实际的图片路径）
image = PIL.Image.open('dog.png')

# 创建 Google Generative AI 客户端，使用 API 密钥进行身份验证
client = genai.Client(api_key=API_KEY)

# 定义文本输入，描述要对图片进行的修改
text_input = ('这张图片里是一只狗。请你将它更换服装、帽子、手上的物品等。'
            '请保持狗的品种不变，并生成一张高质量的艺术图片。',)

# 设置生成次数
generation_count = 500  # 可以修改这个数字来改变生成次数

# 循环生成图片
total_image_count = 0  # 总图片计数器
for i in range(generation_count):
    print(f"正在进行第 {i+1}/{generation_count} 次生成...")

    # 调用 Gemini 模型生成内容
    # 使用支持图像生成的 Gemini 2.0 Flash 预览版模型
    response = client.models.generate_content(
        model="gemini-2.0-flash-preview-image-generation",  # 指定模型
        contents=[text_input, image],  # 输入内容：文本描述和原始图片
        config=types.GenerateContentConfig(
          response_modalities=['TEXT', 'IMAGE']  # 指定响应类型：文本和图像
        )
    )

    # 处理模型返回的结果
    for part in response.candidates[0].content.parts:
      if part.text is not None:
        # 如果返回的是文本内容，打印到控制台
        print("AI 回复:", part.text)
      elif part.inline_data is not None:
        try:
          # 改进的图像数据处理逻辑
          image_data = part.inline_data.data
          print(f"图像数据类型: {type(image_data)}")
          print(f"图像数据长度: {len(image_data) if hasattr(image_data, '__len__') else 'Unknown'}")

          # 如果数据是字符串，可能是base64编码
          if isinstance(image_data, str):
            image_data = base64.b64decode(image_data)

          # 创建BytesIO对象并确保指针在开始位置
          image_buffer = BytesIO(image_data)
          image_buffer.seek(0)

          # 将图像数据转换为PIL图像对象
          generated_image = Image.open(image_buffer)

          # 生成简化的文件名
          filename = f"dog_{total_image_count:03d}.png"
          filepath = os.path.join(output_dir, filename)

          # 保存图片到本地
          generated_image.save(filepath)
          print(f"图片已保存到: {filepath}")

          # 显示生成的图片
          generated_image.show()

          total_image_count += 1

        except Exception as e:
          print(f"处理图像时出错: {e}")
          print(f"错误类型: {type(e)}")
          # 保存原始数据以供调试
          fallback_filename = f"fallback_image_{total_image_count:03d}.png"
          fallback_filepath = os.path.join(output_dir, fallback_filename)
          try:
            with open(fallback_filepath.replace('.png', '.bin'), 'wb') as f:
              if isinstance(part.inline_data.data, str):
                f.write(part.inline_data.data.encode())
              else:
                f.write(part.inline_data.data)
            print(f"原始图像数据已保存到: {fallback_filepath.replace('.png', '.bin')} 供调试使用")
          except Exception as save_error:
            print(f"保存调试数据时也出错: {save_error}")

          total_image_count += 1

print(f"处理完成！共生成 {total_image_count} 张图片")